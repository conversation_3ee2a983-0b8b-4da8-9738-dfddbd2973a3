name: 'generate_embeddings'
on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'docs/**'

jobs:
  generate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: supabase/embeddings-generator@v0.0.5
        with:
          supabase-url: ${{ secrets.SUPABASE_URL }}
          supabase-service-role-key: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
          openai-key: ${{ secrets.OPENAI_DOC_EMBEDDINGS_KEY }}
          docs-root-path: 'docs'