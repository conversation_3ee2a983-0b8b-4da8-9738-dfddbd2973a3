name: ESLint Code Quality Checks

on:
  pull_request:
    branches:
      - main
      - dev
      - release/*
    paths:
      - 'api/**'
      - 'client/**'

jobs:
  eslint_checks:
    name: Run ESLint Linting
    runs-on: ubuntu-latest
    permissions:
      contents: read
      security-events: write
      actions: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: npm

      - name: Install dependencies
        run: npm ci

      # Run ESLint on changed files within the api/ and client/ directories.
      - name: Run ESLint on changed files
        env:
          SARIF_ESLINT_IGNORE_SUPPRESSED: "true"
        run: |
          # Extract the base commit SHA from the pull_request event payload.
          BASE_SHA=$(jq --raw-output .pull_request.base.sha "$GITHUB_EVENT_PATH")
          echo "Base commit SHA: $BASE_SHA"

          # Get changed files (only JS/TS files in api/ or client/)
          CHANGED_FILES=$(git diff --name-only --diff-filter=ACMRTUXB "$BASE_SHA" HEAD | grep -E '^(api|client)/.*\.(js|jsx|ts|tsx)$' || true)

          # Debug output
          echo "Changed files:"
          echo "$CHANGED_FILES"

          # Ensure there are files to lint before running ESLint
          if [[ -z "$CHANGED_FILES" ]]; then
            echo "No matching files changed. Skipping ESLint."
            echo "UPLOAD_SARIF=false" >> $GITHUB_ENV
            exit 0
          fi

          # Set variable to allow SARIF upload
          echo "UPLOAD_SARIF=true" >> $GITHUB_ENV

          # Run ESLint
          npx eslint --no-error-on-unmatched-pattern \
            --config eslint.config.mjs \
            --format @microsoft/eslint-formatter-sarif \
            --output-file eslint-results.sarif $CHANGED_FILES || true

      - name: Upload analysis results to GitHub
        if: env.UPLOAD_SARIF == 'true'
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: eslint-results.sarif
          wait-for-processing: true