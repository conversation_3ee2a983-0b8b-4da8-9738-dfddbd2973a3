name: Generate Unreleased Changelog PR

on:
  schedule:
    - cron: "0 0 * * 1"  # Runs every Monday at 00:00 UTC
  workflow_dispatch:

jobs:
  generate-unreleased-changelog-pr:
    permissions:
      contents: write    # Needed for pushing commits and creating branches.
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      # 1. Checkout the repository on main.
      - name: Checkout Repository on Main
        uses: actions/checkout@v4
        with:
          ref: main
          fetch-depth: 0

      # 4. Get the latest version tag.
      - name: Get Latest Tag
        id: get_latest_tag
        run: |
          LATEST_TAG=$(git describe --tags $(git rev-list --tags --max-count=1) || echo "none")
          echo "Latest tag: $LATEST_TAG"
          echo "tag=$LATEST_TAG" >> $GITHUB_OUTPUT

      # 5. Generate the Unreleased changelog.
      - name: Generate Unreleased Changelog
        id: generate_unreleased
        uses: mikepenz/release-changelog-builder-action@v5.1.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          configuration: ".github/configuration-unreleased.json"
          owner: ${{ github.repository_owner }}
          repo: ${{ github.event.repository.name }}
          outputFile: CHANGELOG-unreleased.md
          fromTag: ${{ steps.get_latest_tag.outputs.tag }}
          toTag: main

      # 7. Update CHANGELOG.md with the new Unreleased section.
      - name: Update CHANGELOG.md
        id: update_changelog
        run: |
          # Create CHANGELOG.md if it doesn't exist.
          if [ ! -f CHANGELOG.md ]; then
            echo "# Changelog" > CHANGELOG.md
            echo "" >> CHANGELOG.md
            echo "All notable changes to this project will be documented in this file." >> CHANGELOG.md
            echo "" >> CHANGELOG.md
          fi
          
          echo "Updating CHANGELOG.md…"
          
          # Extract content before the "## [Unreleased]" (or first version header if missing).
          if grep -q "^## \[Unreleased\]" CHANGELOG.md; then
            awk '/^## \[Unreleased\]/{exit} {print}' CHANGELOG.md > CHANGELOG_TMP.md
          else
            awk '/^## \[v/{exit} {print}' CHANGELOG.md > CHANGELOG_TMP.md
          fi
          
          # Append the generated Unreleased changelog.
          echo "" >> CHANGELOG_TMP.md
          cat CHANGELOG-unreleased.md >> CHANGELOG_TMP.md
          echo "" >> CHANGELOG_TMP.md
          
          # Append the remainder of the original changelog (starting from the first version header).
          awk 'f{print} /^## \[v/{f=1; print}' CHANGELOG.md >> CHANGELOG_TMP.md
          
          # Replace the old file with the updated file.
          mv CHANGELOG_TMP.md CHANGELOG.md
          
          # Remove the temporary generated file.
          rm -f CHANGELOG-unreleased.md
          
          echo "Final CHANGELOG.md:"
          cat CHANGELOG.md

      # 8. Check if CHANGELOG.md has any updates.
      - name: Check for CHANGELOG.md changes
        id: changelog_changes
        run: |
          if git diff --quiet CHANGELOG.md; then
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "has_changes=true" >> $GITHUB_OUTPUT
          fi

      # 9. Create (or update) the Pull Request only if there are changes.
      - name: Create Pull Request
        if: steps.changelog_changes.outputs.has_changes == 'true'
        uses: peter-evans/create-pull-request@v7
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          base: main
          branch: "changelog/unreleased-update"
          sign-commits: true
          commit-message: "action: update Unreleased changelog"
          title: "action: update Unreleased changelog"
          body: |
            **Description**:
            - This PR updates the Unreleased section in CHANGELOG.md.
            - It compares the current main branch with the latest version tag (determined as ${{ steps.get_latest_tag.outputs.tag }}),
              regenerates the Unreleased changelog, removes any old Unreleased block, and inserts the new content.